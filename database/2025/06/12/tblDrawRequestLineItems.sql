CREATE TABLE `tblDrawRequestLineItems` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `drawId` INT NOT NULL,
    `categoryId` INT NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `order` INT UNSIGNED NOT NULL DEFAULT 1,
    `cost` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `completedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `completedPercent` DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    `notes` TEXT NULL,
    `lenderNotes` TEXT NULL,
    `rejectReason` TEXT NULL,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX IX_DrawRequestLineItems_Draw_Order (drawId, `order`),
    INDEX IX_DrawRequestLineItems_Category_Order (categoryId, `order`),
    CONSTRAINT FK_DrawRequestLineItems_Draw FOREIGN KEY (drawId)
        REFERENCES tblDrawRequests(id) ON DELETE CASCADE,
    CONSTRAINT FK_DrawRequestLineItems_Category FOREIGN KEY (categoryId)
        REFERENCES tblDrawRequestCategories(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
