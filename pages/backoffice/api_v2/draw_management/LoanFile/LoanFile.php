<?php

namespace pages\backoffice\api_v2\draw_management\loanFile;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\cypher;
use models\composite\oDrawManagement\DrawRequestManager;

/**
 * Class loanFile
 *
 * API endpoint for updating and fetching draw request data
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class loanFile extends BackofficePage
{
    /**
     * Handle POST requests to update draw request data
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = file_get_contents("php://input");
        $postData = json_decode($postData, true);

        $LMRId = $postData['lmrid'];
        if ($LMRId && !is_numeric($LMRId)) $LMRId = cypher::myDecryption($LMRId);
        $LMRId = (int)$LMRId;

        if (!$LMRId) {
            HTTP::ExitJSON(["success" => false, "message" => "Loan file ID is required."]);
        }

        try {
            $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
            $result = $drawRequestManager->saveDrawRequestData($postData);

            if (!$result) {
                HTTP::ExitJSON(["success" => false, "message" => "Failed to save draw request data."]);
            }
            HTTP::ExitJSON(["success" => true, "message" => "Draw request data saved successfully."]);
        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }
}
