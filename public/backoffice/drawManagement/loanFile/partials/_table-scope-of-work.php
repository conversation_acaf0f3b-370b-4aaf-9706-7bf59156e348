<table class="table table-hover table-bordered table-vertical-center ">
    <thead>
        <tr>
            <th style="width: 400px;">Line Item</th>
            <th>Total Budget</th>
            <th>Completed Renovations</th>
            <th>% Completed</th>
            <th style="width: 70px;">Borrower Notes</th>
            <th style="width: 70px;">Lender Notes</th>
            <th style="width: 200px;" class="hide col-reject-reason">Reject Reason</th>
        </tr>
    </thead>
    <tbody>
        <?php if (!empty($categoriesData)): ?>
            <?php foreach ($categoriesData as $category): ?>
                <?php if (!empty($category['lineItems'])): ?>
                    <tr class="category-header">
                        <td colspan="7">
                            <?= htmlspecialchars(strtoupper($category['name'])) ?>
                            <?php if (!empty($category['description'])): ?>
                            <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                title="<?= htmlspecialchars($category['description']) ?>"></i>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if (!empty($category['lineItems'])): ?>
                        <?php foreach ($category['lineItems'] as $lineItem): ?>
                            <tr class="line-item">
                                <td>
                                    <?= htmlspecialchars($lineItem['name']) ?>
                                    <?php if (!empty($lineItem['description'])): ?>
                                        <i class="fa fa-info-circle text-primary tooltipClass ml-2" title="<?= htmlspecialchars($lineItem['description']) ?>"></i>
                                    <?php endif; ?>
                                </td>
                                <td>$<?= $lineItem['cost'] ?></td>
                                <td>$<?= $lineItem['completedAmount'] ?></td>
                                <td>
                                    <span class="percentage"><?= round($lineItem['completedPercent']) ?>%</span>
                                </td>
                                <td style="text-align: center;">
                                    <button class="btn note-btn btn-sm" type="button">
                                        <i class="fas fa-sticky-note tooltipClass <?= !empty($lineItem['notes']) ? 'text-primary' : 'text-muted' ?>"
                                            data-original-title="<?= htmlspecialchars($lineItem['notes']) ?>">
                                        </i>
                                    </button>
                                </td>
                                <td style="text-align: center;">
                                    <button class="btn lender-note-btn btn-sm" type="button"
                                        data-line-item-id="<?= $lineItem['id'] ?>">
                                        <i class="fas fa-sticky-note tooltipClass <?= !empty($lineItem['lenderNotes']) ? 'text-primary' : 'text-muted' ?>"
                                            data-original-title="<?= htmlspecialchars($lineItem['lenderNotes']) ?>">
                                        </i>
                                    </button>
                                    <input type="hidden" name="lenderNotes" value="<?= htmlspecialchars($lineItem['lenderNotes']) ?>">
                                </td>
                                <td class="hide col-reject-reason">
                                    <select class="form-control input-sm" name="rejectReason">
                                        <option <?= $lineItem['rejectReason'] === '' ? 'selected' : ''; ?> value="">-- None --</option>
                                        <option <?= $lineItem['rejectReason'] === 'revise_budget' ? 'selected' : ''; ?> value="revise_budget">Revise Budget</option>
                                        <option <?= $lineItem['rejectReason'] === 'not_covered' ? 'selected' : ''; ?> value="not_covered">Line Item not covered</option>
                                        <option <?= $lineItem['rejectReason'] === 'other' ? 'selected' : ''; ?> value="other">Other(See Notes)</option>
                                    </select>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php else: ?>
        <tr>
            <td colspan="7" class="text-center text-muted py-4">
                No draw request data available. Please create categories and line items first.
            </td>
        </tr>
        <?php endif; ?>
    </tbody>
</table>
